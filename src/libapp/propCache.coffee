# 房源查询缓存模块
# 实现按需生成的动态查询缓存系统，支持内存管理和LRU淘汰策略

{getHash} = require '../lib/helpers_string'
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()

# 缓存存储变量 - 作为主缓存容器，在内存中存储所有缓存的查询结果
QUERY_CACHE = {}

# 缓存配置常量
CACHE_DEFAULT_EXPIRATION_MS = 5 * 60 * 1000  # 5分钟
CACHE_STATS_INTERVAL_MS = 60 * 60 * 1000     # 1小时
MAX_CACHE_ENTRIES = 1000                     # 最大缓存条目数
LRU_CLEANUP_THRESHOLD = 800                  # LRU淘汰阈值
LRU_CLEANUP_BATCH_SIZE = 100                 # 每次淘汰的条目数量

# LRU淘汰统计
lruStats = {
  totalCleanups: 0
  expiredCleaned: 0
  lruCleaned: 0
  lastCleanupTime: 0
}

###*
# 生成缓存键
# @param {Object} body - 请求体参数
# @return {String} 缓存键
###
module.exports.generateCacheKey = generateCacheKey = (body) ->
  try
    # 1. 规范化：创建不含useCache字段的body副本
    cleanBody = {}
    for key, value of body
      continue if key is 'useCache'
      cleanBody[key] = value
    
    # 2. 对键进行字母顺序排序
    sortedKeys = Object.keys(cleanBody).sort()
    sortedBody = {}
    for key in sortedKeys
      sortedBody[key] = cleanBody[key]
    
    # 3. 字符串化：转换为紧凑的JSON字符串
    jsonString = JSON.stringify(sortedBody)
    
    # 4. 哈希计算：使用helpers_string.coffee中的getHash函数
    return getHash(jsonString)
  catch error
    debug.error 'generateCacheKey error:', error
    return null

###*
# 清理过期的缓存条目
# @return {Number} 清理的条目数量
###
cleanupExpiredEntries = ->
  now = Date.now()
  expiredKeys = []
  
  for cacheKey, entry of QUERY_CACHE
    if now >= entry.expTs
      expiredKeys.push cacheKey
  
  for key in expiredKeys
    delete QUERY_CACHE[key]
  
  lruStats.expiredCleaned += expiredKeys.length
  return expiredKeys.length

###*
# 执行LRU淘汰策略
# @param {Number} targetCount - 目标清理数量
# @return {Number} 实际清理的条目数量
###
performLRUCleanup = (targetCount) ->
  # 收集所有缓存条目并按lastAccessTime排序
  entries = []
  for cacheKey, entry of QUERY_CACHE
    entries.push {key: cacheKey, lastAccessTime: entry.lastAccessTime}
  
  # 按lastAccessTime升序排序（最旧的在前面）
  entries.sort (a, b) -> a.lastAccessTime - b.lastAccessTime
  
  # 删除最旧的条目
  cleanedCount = 0
  for i in [0...Math.min(targetCount, entries.length)]
    delete QUERY_CACHE[entries[i].key]
    cleanedCount++
  
  lruStats.lruCleaned += cleanedCount
  return cleanedCount

###*
# 内存管理：检查并执行LRU淘汰
###
performMemoryManagement = ->
  currentCount = Object.keys(QUERY_CACHE).length
  
  if currentCount >= LRU_CLEANUP_THRESHOLD
    lruStats.totalCleanups++
    lruStats.lastCleanupTime = Date.now()
    
    debug.info "Cache cleanup triggered, current entries: #{currentCount}"
    
    # 1. 优先清理已过期的条目
    expiredCleaned = cleanupExpiredEntries()
    debug.info "Cleaned expired entries: #{expiredCleaned}"
    
    # 2. 如果清理过期条目后仍超过阈值，执行LRU淘汰
    currentCount = Object.keys(QUERY_CACHE).length
    if currentCount >= LRU_CLEANUP_THRESHOLD
      lruCleaned = performLRUCleanup(LRU_CLEANUP_BATCH_SIZE)
      debug.info "LRU cleaned entries: #{lruCleaned}"
    
    finalCount = Object.keys(QUERY_CACHE).length
    debug.info "Cache cleanup completed, final entries: #{finalCount}"

###*
# 获取缓存条目
# @param {String} cacheKey - 缓存键
# @return {Object|null} 缓存条目或null
###
module.exports.getCacheEntry = getCacheEntry = (cacheKey) ->
  entry = QUERY_CACHE[cacheKey]
  return null unless entry
  
  now = Date.now()
  # 检查是否过期
  if now >= entry.expTs
    delete QUERY_CACHE[cacheKey]
    return null
  
  # 更新最后访问时间和命中次数
  entry.lastAccessTime = now
  entry.hitCount++
  
  return entry

###*
# 设置缓存条目
# @param {String} cacheKey - 缓存键
# @param {Array} props - 查询结果
# @param {Number} queryDuration - 查询耗时
# @param {Object} body - 请求体参数
# @param {Object} oldEntry - 旧的缓存条目（可选）
###
module.exports.setCacheEntry = setCacheEntry = (cacheKey, props, queryDuration, body, oldEntry = null) ->
  # 执行内存管理
  performMemoryManagement()
  
  now = Date.now()
  
  # 创建新的缓存条目
  newEntry = {
    props: props
    ts: now
    expTs: now + CACHE_DEFAULT_EXPIRATION_MS
    lastAccessTime: now
    queryDuration: queryDuration
    totalDuration: if oldEntry then (oldEntry.totalDuration + queryDuration) else queryDuration
    hitCount: if oldEntry then oldEntry.hitCount else 0
    updateCount: if oldEntry then (oldEntry.updateCount + 1) else 1
    body: body
  }
  
  QUERY_CACHE[cacheKey] = newEntry
  debug.debug "Cache entry set for key: #{cacheKey}, total entries: #{Object.keys(QUERY_CACHE).length}"

###*
# 生成缓存统计报告
###
logCacheReport = ->
  try
    entries = Object.values(QUERY_CACHE)
    now = Date.now()
    
    # 基础统计
    totalEntries = entries.length
    totalHits = entries.reduce(((sum, entry) -> sum + entry.hitCount), 0)
    totalUpdates = entries.reduce(((sum, entry) -> sum + entry.updateCount), 0)
    expiredCount = entries.filter((entry) -> now >= entry.expTs).length
    memoryUsageRate = (totalEntries / MAX_CACHE_ENTRIES * 100).toFixed(2)
    
    # 估算内存占用（简单估算）
    estimatedMemoryKB = (totalEntries * 2).toFixed(2) # 每个条目约2KB
    
    console.log """
    ========== 房源查询缓存统计报告 ==========
    报告时间: #{new Date().toISOString()}
    
    === 缓存概况 ===
    缓存条目总数: #{totalEntries}
    最大条目限制: #{MAX_CACHE_ENTRIES}
    内存使用率: #{memoryUsageRate}%
    过期条目数量: #{expiredCount}
    估算内存占用: #{estimatedMemoryKB} KB
    
    === 访问统计 ===
    总命中次数: #{totalHits}
    总更新次数: #{totalUpdates}
    平均命中率: #{if totalUpdates > 0 then (totalHits / totalUpdates).toFixed(2) else 'N/A'}
    
    === LRU淘汰统计 ===
    总清理次数: #{lruStats.totalCleanups}
    过期清理数: #{lruStats.expiredCleaned}
    LRU清理数: #{lruStats.lruCleaned}
    最后清理时间: #{if lruStats.lastCleanupTime > 0 then new Date(lruStats.lastCleanupTime).toISOString() else 'N/A'}
    
    ==========================================
    """
    
  catch error
    debug.error 'logCacheReport error:', error

# 启动定时统计报告
setInterval(logCacheReport, CACHE_STATS_INTERVAL_MS)
